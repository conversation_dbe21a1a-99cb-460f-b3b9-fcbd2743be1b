import 'jest-preset-angular/setup-jest'
import '@angular/localize/init'
/**
 * @link https://stackoverflow.com/a/48750402/4444844
 */
import <PERSON><PERSON> from 'dexie'
import indexedDB from 'fake-indexeddb'
import { TextEncoder, TextDecoder } from 'util'

global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

Dexie.dependencies.indexedDB = indexedDB
;(global as any).URL.createObjectURL = jest.fn(() => 'dummy-url')
;(global as any).URL.revokeObjectURL = jest.fn()
global.encryptStr = jest.fn()
global.decryptStr = jest.fn()

global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock PSPDFKit globally to prevent TextMetrics errors in Jest environment
jest.mock('pspdfkit', () => ({
  ZoomMode: {
    FIT_TO_WIDTH: 'FIT_TO_WIDTH',
    FIT_TO_VIEWPORT: 'FIT_TO_VIEWPORT',
    AUTO: 'AUTO',
  },
  load: jest.fn().mockResolvedValue({}),
  unload: jest.fn(),
}))
