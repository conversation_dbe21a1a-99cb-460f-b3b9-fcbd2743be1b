import { FeaturePdfViewerComponent } from './feature-pdf-viewer.component'
import { PageControlActionType } from '@venio/data-access/review'
import PSPDFKit from 'pspdfkit'

// Mock PSPDFKit
jest.mock('pspdfkit', () => ({
  ZoomMode: {
    FIT_TO_WIDTH: 'FIT_TO_WIDTH',
    FIT_TO_VIEWPORT: 'FIT_TO_VIEWPORT',
    AUTO: 'AUTO',
  },
  load: jest.fn().mockResolvedValue({}),
  unload: jest.fn(),
}))

describe('FeaturePdfViewerComponent - Zoom Factor Preservation', () => {
  let component: FeaturePdfViewerComponent
  let mockViewerInstance: any

  beforeEach(() => {
    // Create a minimal component instance for testing zoom functionality
    component = Object.create(FeaturePdfViewerComponent.prototype)

    // Initialize the currentZoomLevel property
    component['currentZoomLevel'] = PSPDFKit.ZoomMode.FIT_TO_WIDTH

    // Create mock viewer instance
    mockViewerInstance = {
      viewState: {
        get: jest.fn().mockReturnValue(PSPDFKit.ZoomMode.FIT_TO_WIDTH),
        set: jest.fn().mockReturnValue({
          set: jest.fn().mockReturnThis(),
        }),
      },
      setViewState: jest.fn().mockImplementation((callback) => {
        // Simulate the callback execution to properly test zoom level updates
        if (typeof callback === 'function') {
          const mockViewState = {
            set: jest.fn().mockReturnThis(),
            zoomIn: jest.fn().mockReturnThis(),
            zoomOut: jest.fn().mockReturnThis(),
          }
          callback(mockViewState)
        }
      }),
      setAnnotationToolbarItems: jest.fn(),
    }

    component.viewerInstance = mockViewerInstance
    component.pageNumberList = [1, 2, 3, 4, 5]
    component.currentPageNumber = 1
    component.selectedImage = 'OriginalImage'
    component.exportDetails = []
    component.selectedExportId = null
    component['cdr'] = {
      markForCheck: jest.fn(),
      detach: jest.fn(),
      detectChanges: jest.fn(),
      checkNoChanges: jest.fn(),
      reattach: jest.fn(),
    } as any
  })

  describe('Zoom Level Initialization', () => {
    it('should initialize with FIT_TO_WIDTH zoom mode', () => {
      expect(component['currentZoomLevel']).toBe(PSPDFKit.ZoomMode.FIT_TO_WIDTH)
    })
  })

  describe('Zoom Level Preservation During Document Loading', () => {
    it('should preserve current zoom level when loading a new document', () => {
      // Arrange
      const customZoomLevel = 1.5
      mockViewerInstance.viewState.get.mockReturnValue(customZoomLevel)
      component.viewerInstance = mockViewerInstance

      // Act
      component['loadPdfDocument'](false)

      // Assert
      expect(mockViewerInstance.viewState.get).toHaveBeenCalledWith('zoom')
      expect(component['currentZoomLevel']).toBe(customZoomLevel)
    })

    it('should apply preserved zoom level in setViewState', () => {
      // Arrange
      const customZoomLevel = 2.0
      component['currentZoomLevel'] = customZoomLevel
      component.viewerInstance = mockViewerInstance

      // Act
      component.setViewState()

      // Assert
      expect(mockViewerInstance.setViewState).toHaveBeenCalled()
    })
  })

  describe('Zoom Operations Update Current Zoom Level', () => {
    it('should update currentZoomLevel when zooming in', () => {
      // Arrange
      const newZoomLevel = 1.25
      mockViewerInstance.viewState.get.mockReturnValue(newZoomLevel)

      // Act
      component['zoomIn']()

      // Assert
      expect(mockViewerInstance.setViewState).toHaveBeenCalled()
      expect(mockViewerInstance.viewState.get).toHaveBeenCalledWith('zoom')
      expect(component['currentZoomLevel']).toBe(newZoomLevel)
    })

    it('should update currentZoomLevel when zooming out', () => {
      // Arrange
      const newZoomLevel = 0.75
      mockViewerInstance.viewState.get.mockReturnValue(newZoomLevel)

      // Act
      component['zoomOut']()

      // Assert
      expect(mockViewerInstance.setViewState).toHaveBeenCalled()
      expect(mockViewerInstance.viewState.get).toHaveBeenCalledWith('zoom')
      expect(component['currentZoomLevel']).toBe(newZoomLevel)
    })

    it('should update currentZoomLevel when setting FIT_TO_WIDTH', async () => {
      // Act
      await component.actionButtonHandler(PageControlActionType.FIT_TO_WIDTH)

      // Assert
      expect(component['currentZoomLevel']).toBe(PSPDFKit.ZoomMode.FIT_TO_WIDTH)
      expect(mockViewerInstance.setViewState).toHaveBeenCalled()
    })

    it('should update currentZoomLevel when setting FIT_TO_HEIGHT', async () => {
      // Arrange - Reset the zoom level to ensure we can see the change
      component['currentZoomLevel'] = PSPDFKit.ZoomMode.FIT_TO_WIDTH

      // Act
      await component.actionButtonHandler(PageControlActionType.FIT_TO_HEIGHT)

      // Assert - The component should update currentZoomLevel to FIT_TO_VIEWPORT
      expect(component['currentZoomLevel']).toBe(
        PSPDFKit.ZoomMode.FIT_TO_VIEWPORT
      )
      expect(mockViewerInstance.setViewState).toHaveBeenCalled()
    })

    it('should update currentZoomLevel when setting ACTUAL_SIZE', async () => {
      // Arrange - Reset the zoom level to ensure we can see the change
      component['currentZoomLevel'] = PSPDFKit.ZoomMode.FIT_TO_WIDTH

      // Act
      await component.actionButtonHandler(PageControlActionType.ACTUAL_SIZE)

      // Assert - The component should update currentZoomLevel to AUTO
      expect(component['currentZoomLevel']).toBe(PSPDFKit.ZoomMode.AUTO)
      expect(mockViewerInstance.setViewState).toHaveBeenCalled()
    })
  })

  describe('Navigation Methods Preserve Zoom Level', () => {
    beforeEach(() => {
      // Set up a custom zoom level to verify it's preserved
      component['currentZoomLevel'] = 1.5
      component.pageNumberList = [1, 2, 3, 4, 5]
      component.currentPageNumber = 3
    })

    it('should preserve zoom level when navigating to next page', () => {
      // Arrange
      const originalZoomLevel = component['currentZoomLevel']

      // Act
      component.navigateToNextPage()

      // Assert
      expect(component['currentZoomLevel']).toBe(originalZoomLevel)
    })

    it('should preserve zoom level when navigating to previous page', () => {
      // Arrange
      const originalZoomLevel = component['currentZoomLevel']

      // Act
      component.navigateToPreviousPage()

      // Assert
      expect(component['currentZoomLevel']).toBe(originalZoomLevel)
    })

    it('should preserve zoom level when navigating to first page', () => {
      // Arrange
      const originalZoomLevel = component['currentZoomLevel']

      // Act
      component.navigateToFirstPage()

      // Assert
      expect(component['currentZoomLevel']).toBe(originalZoomLevel)
    })

    it('should preserve zoom level when navigating to last page', () => {
      // Arrange
      const originalZoomLevel = component['currentZoomLevel']

      // Act
      component.navigateToLastPage()

      // Assert
      expect(component['currentZoomLevel']).toBe(originalZoomLevel)
    })

    it('should preserve zoom level when navigating to specific page', () => {
      // Arrange
      const originalZoomLevel = component['currentZoomLevel']
      const targetPage = 2

      // Act
      component.navigateToPage(targetPage)

      // Assert
      expect(component['currentZoomLevel']).toBe(originalZoomLevel)
    })
  })
})
